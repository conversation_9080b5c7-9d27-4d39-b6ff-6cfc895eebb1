��������: 5, ��Դ��������: 3
��ȡ allocation ����:
��ȡ max ����:
��ȡ available ����:
#########################################
Allocation Matrix
P0		0	1	0	
P1		2	0	0	
P2		3	0	2	
P3		2	1	1	
P4		0	0	2	
Max Matrix
P0		7	5	3	
P1		3	2	2	
P2		9	0	2	
P3		2	2	2	
P4		4	3	3	
Available Vector
3	3	2	
with additional resouces requests
P0		0	2	0	
start...
P3 finished
P1 finished
P0 finished
P2 finished
P4 finished
safe!!
Finish...
