# 银行家算法 - 文件输入输出版本

## 概述
这是银行家算法的文件输入输出版本，支持从文件读取输入数据并将结果输出到文件。

## 输入文件格式 (input.txt)

输入文件应按以下格式组织：

```
第1行: 进程数量 资源类型数量
第2-n+1行: allocation矩阵 (已分配资源矩阵)
第n+2-2n+1行: max矩阵 (最大需求矩阵)  
第2n+2行: available向量 (可用资源向量)
第2n+3行: 请求进程ID
第2n+4行: 请求向量
```

## 示例输入文件

```
5 3
0 1 0
2 0 0
3 0 2
2 1 1
0 0 2
7 5 3
3 2 2
9 0 2
2 2 2
4 3 3
3 3 2
0
0 2 0
```

### 解释：
- 5个进程，3种资源类型
- allocation矩阵：5行3列，表示每个进程已分配的资源
- max矩阵：5行3列，表示每个进程的最大资源需求
- available向量：3个元素，表示当前可用的资源
- 请求进程ID：0 (表示P0进程)
- 请求向量：0 2 0 (P0进程请求0个资源1，2个资源2，0个资源3)

## 输出文件 (output.txt)

程序会将所有输出同时写入控制台和output.txt文件，包括：
- 输入数据的确认
- allocation矩阵、max矩阵、available向量的显示
- 请求处理过程
- 安全性检查结果
- 进程完成顺序

## 编译和运行

```bash
gcc -o demo demo.c -lpthread
./demo
```

## 特性

1. **文件输入输出**：从input.txt读取数据，结果输出到output.txt
2. **错误处理**：完善的文件读取错误处理
3. **线程安全**：使用互斥锁保护共享变量
4. **双重输出**：同时输出到控制台和文件
5. **实时刷新**：使用fflush确保输出及时写入文件

## 注意事项

- 确保input.txt文件存在且格式正确
- 程序会自动创建output.txt文件
- 所有共享变量访问都通过互斥锁同步
- 支持多线程并发执行银行家算法
