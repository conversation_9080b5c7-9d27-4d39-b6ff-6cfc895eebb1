#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>


/**********************************************************************************
银行家算法是用于避免死锁的，本次实验，我们要模拟出这样的一种有可能产生死锁的情形：
系统中目前有很多线程，每个线程都有自己的最大资源需求、也已经分配了一些资源、还需要一些资源。
在这种情形下，系统容易发生死锁(原因思考下)。
然后，我们使用银行家算法来找到这些线程的一种可能的执行顺序，保证不会发生死锁。
************************************************************************************/


/*********************************************************************************
修改为文件输入输出模式，输入文件格式：
第一行：进程数量 资源类型数量
接下来n行：allocation矩阵
接下来n行：max矩阵
接下来一行：available向量
最后两行：请求进程ID 和 请求向量
**********************************************************************************/


int is_safe_state();

#define MAX_PRO 256   /* max process count */
#define MAX_RES 256	/* max resource type */

int available[MAX_RES];
int max[MAX_PRO][MAX_RES];
int allocation[MAX_PRO][MAX_RES];
int need[MAX_PRO][MAX_RES];
int request[MAX_RES];
int m,n;

// 文件指针
FILE *input_file;
FILE *output_file;

int main(int argc, char *argv[])
{
	int id;
	int i,j;

	// 打开输入文件
	input_file = fopen("input.txt", "r");
	if (input_file == NULL) {
		printf("无法打开输入文件 input.txt\n");
		return -1;
	}

	// 打开输出文件
	output_file = fopen("output.txt", "w");
	if (output_file == NULL) {
		printf("无法创建输出文件 output.txt\n");
		fclose(input_file);
		return -1;
	}

	// 从文件读取进程数量和资源类型数量
	if (fscanf(input_file, "%d %d", &n, &m) != 2) {
		printf("读取进程数量和资源类型数量失败\n");
		fclose(input_file);
		fclose(output_file);
		return -1;
	}

	fprintf(output_file, "进程数量: %d, 资源类型数量: %d\n", n, m);
	printf("进程数量: %d, 资源类型数量: %d\n", n, m);

	if (m>256 || n>256 || m<0 || n<0)
	{
		printf("输入错误：进程数量或资源类型数量超出范围\n");
		fprintf(output_file, "输入错误：进程数量或资源类型数量超出范围\n");
		fclose(input_file);
		fclose(output_file);
		return -1;
	}

	// 从文件读取已分配资源矩阵
	fprintf(output_file, "读取 allocation 矩阵:\n");
	printf("读取 allocation 矩阵:\n");
	for (i=0;i<n;i++) {
		for (j=0;j<m;j++) {
			if (fscanf(input_file, "%d", &allocation[i][j]) != 1) {
				printf("读取 allocation 矩阵失败\n");
				fclose(input_file);
				fclose(output_file);
				return -1;
			}
		}
	}

	// 从文件读取最大资源需求矩阵
	fprintf(output_file, "读取 max 矩阵:\n");
	printf("读取 max 矩阵:\n");
	for (i=0;i<n;i++) {
		for (j=0;j<m;j++) {
			if (fscanf(input_file, "%d", &max[i][j]) != 1) {
				printf("读取 max 矩阵失败\n");
				fclose(input_file);
				fclose(output_file);
				return -1;
			}
		}
	}

	// 从文件读取可用资源向量
	fprintf(output_file, "读取 available 向量:\n");
	printf("读取 available 向量:\n");
	for (j=0;j<m;j++) {
		if (fscanf(input_file, "%d", &available[j]) != 1) {
			printf("读取 available 向量失败\n");
			fclose(input_file);
			fclose(output_file);
			return -1;
		}
	}

	// 计算初始need矩阵
	for (i=0;i<n;i++)
		for (j=0;j<m;j++)
			need[i][j] = max[i][j] - allocation[i][j];

	// 从文件读取请求进程ID
	if (fscanf(input_file, "%d", &id) != 1) {
		printf("读取请求进程ID失败\n");
		fclose(input_file);
		fclose(output_file);
		return -1;
	}

	// 从文件读取请求向量
	for (j=0;j<m;j++) {
		if (fscanf(input_file, "%d", &request[j]) != 1) {
			printf("读取请求向量失败\n");
			fclose(input_file);
			fclose(output_file);
			return -1;
		}
	}

	// 关闭输入文件
	fclose(input_file);


	printf ("#########################################\n");
	fprintf(output_file, "#########################################\n");

	printf ("Allocation Matrix\n");
	fprintf(output_file, "Allocation Matrix\n");
	for (i=0;i<n;i++)
	{
		printf ("P%d\t\t",i);
		fprintf(output_file, "P%d\t\t",i);
		for (j=0;j<m;j++) {
			printf ("%d\t",allocation[i][j]);
			fprintf(output_file, "%d\t",allocation[i][j]);
		}
		printf ("\n");
		fprintf(output_file, "\n");
	}

	printf ("Max Matrix\n");
	fprintf(output_file, "Max Matrix\n");
	for (i=0;i<n;i++)
	{
		printf ("P%d\t\t",i);
		fprintf(output_file, "P%d\t\t",i);
		for (j=0;j<m;j++) {
			printf ("%d\t",max[i][j]);
			fprintf(output_file, "%d\t",max[i][j]);
		}
		printf ("\n");
		fprintf(output_file, "\n");
	}

	printf ("Available Vector\n");
	fprintf(output_file, "Available Vector\n");
	for (j=0;j<m;j++) {
		printf ("%d\t",available[j]);
		fprintf(output_file, "%d\t",available[j]);
	}
	printf ("\n");
	fprintf(output_file, "\n");

	printf ("with additional resouces requests\nP%d\t\t",id);
	fprintf(output_file, "with additional resouces requests\nP%d\t\t",id);
	for (j=0;j<m;j++) {
		printf ("%d\t", request[j]);
		fprintf(output_file, "%d\t", request[j]);
	}
	printf ("\n");
	fprintf(output_file, "\n");

	// 银行家算法请求处理
	printf("开始处理资源请求...\n");
	fprintf(output_file, "开始处理资源请求...\n");

	// 第一步：检查请求是否合法
	for (j=0;j<m;j++)
	{
		// 检查请求是否超过need
		if (request[j] > need[id][j]) {
			printf("错误：请求超过进程需求！Request[%d]=%d > Need[%d][%d]=%d\n",
				j, request[j], id, j, need[id][j]);
			fprintf(output_file, "错误：请求超过进程需求！Request[%d]=%d > Need[%d][%d]=%d\n",
				j, request[j], id, j, need[id][j]);
			fclose(output_file);
			return 0;
		}
		// 检查请求是否超过available
		if (request[j] > available[j]) {
			printf("错误：请求超过可用资源！Request[%d]=%d > Available[%d]=%d\n",
				j, request[j], j, available[j]);
			fprintf(output_file, "错误：请求超过可用资源！Request[%d]=%d > Available[%d]=%d\n",
				j, request[j], j, available[j]);
			fclose(output_file);
			return 0;
		}
	}

	// 第二步：试探性分配资源
	printf("试探性分配资源...\n");
	fprintf(output_file, "试探性分配资源...\n");

	for (j=0;j<m;j++) {
		// 修改available矩阵
		available[j] -= request[j];
		// 修改allocation矩阵
		allocation[id][j] += request[j];
		// 修改need矩阵
		need[id][j] -= request[j];
	}

	// 第三步：检查系统安全性
	printf("检查系统安全性...\n");
	fprintf(output_file, "检查系统安全性...\n");

	if (is_safe_state()) {
		printf("系统处于安全状态，请求被接受！\n");
		fprintf(output_file, "系统处于安全状态，请求被接受！\n");
	} else {
		printf("系统处于不安全状态，请求被拒绝！\n");
		fprintf(output_file, "系统处于不安全状态，请求被拒绝！\n");

		// 回滚资源分配
		for (j=0;j<m;j++) {
			available[j] += request[j];
			allocation[id][j] -= request[j];
			need[id][j] += request[j];
		}
	}

	// 关闭输出文件
	fclose(output_file);

	return 0;
}

// 安全性检查函数
int is_safe_state() {
	int work_temp[MAX_RES];
	int finish_temp[MAX_PRO];
	int i, j, k;
	int count = 0;

	// 初始化临时工作向量和完成标志
	for (j = 0; j < m; j++) {
		work_temp[j] = available[j];
	}
	for (i = 0; i < n; i++) {
		finish_temp[i] = 0;
	}



	// 银行家算法安全性检查
	while (count < n) {
		int found = 0;
		for (i = 0; i < n; i++) {
			if (!finish_temp[i]) {
				// 检查进程i的需求是否可以被满足
				int can_allocate = 1;
				for (j = 0; j < m; j++) {
					if (need[i][j] > work_temp[j]) {
						can_allocate = 0;
						break;
					}
				}

				if (can_allocate) {
					// 模拟分配资源给进程i
					for (j = 0; j < m; j++) {
						work_temp[j] += allocation[i][j];
					}
					finish_temp[i] = 1;
					found = 1;
					count++;

					printf("安全序列: P%d 完成\n", i);
					fprintf(output_file, "安全序列: P%d 完成\n", i);
					break;
				}
			}
		}

		if (!found) {
			// 没有找到可以完成的进程，系统不安全
			return 0;
		}
	}

	return 1; // 系统安全
}
