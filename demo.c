#include <pthread.h>
#include <stdio.h>

/**********************************************************************************
银行家算法是用于避免死锁的，本次实验，我们要模拟出这样的一种有可能产生死锁的情形：
系统中目前有很多线程，每个线程都有自己的最大资源需求、也已经分配了一些资源、还需要一些资源。
在这种情形下，系统容易发生死锁(原因思考下)。
然后，我们使用银行家算法来找到这些线程的一种可能的执行顺序，保证不会发生死锁。
************************************************************************************/


/*********************************************************************************
下列代码的各种矩阵是用手动输入的方式，建议同学们改成读文件的方式，这样方便调试
**********************************************************************************/


void *start_banker(void *param);

#define MAX_PRO 256   /* max process count */
#define MAX_RES 256	/* max resource type */

int available[MAX_RES];
int max[MAX_PRO][MAX_RES];
int allocation[MAX_PRO][MAX_RES];
int need[MAX_PRO][MAX_RES];
int request[MAX_RES];
int work[MAX_RES];
int finish[MAX_PRO];
int stop_flag = 0;
int m,n;
pthread_mutex_t mutex;
int proc_ids[MAX_PRO];

int main(int argc, char *argv[])
{
	pthread_t tid[MAX_PRO];
	pthread_attr_t attr[MAX_PRO];

	int id;
	int i,j;
  //输入当前系统中有几个并发的线程
	printf ("please input the process count(<=%d).\n",MAX_PRO);
	scanf("%d", &n);
  //输入当前系统中有几种资源
	printf ("please input the resource type count(<=%d).\n",MAX_RES);
	scanf("%d", &m);
	
	if (m>256 || n>256 || m<0 || n<0)
	{
		printf ("input error\n");
		return -1;
	}
	//输入已经分配的资源矩阵
	printf ("please input allocation matrix\n");
	for (i=0;i<n;i++)
		for (j=0;j<m;j++)
			scanf("%d", &allocation[i][j]);
			
	//输入最大资源需求矩阵
	printf ("please input max matrix\n");
	for (i=0;i<n;i++)
		for (j=0;j<m;j++)
			scanf("%d", &max[i][j]);

  //输入当前系统中还可得的资源向量
	printf ("please input available vector\n");
	for (j=0;j<m;j++)
		scanf("%d", &available[j]);

	for (i=0;i<n;i++)
		for (j=0;j<m;j++)
			need[i][j] = max[i][j] - allocation[i][j];  // 计算need矩阵

  //当前某个线程又发出了新的请求，还记得上课讲的发出新请求的银行家算法吗？
  //输入是哪个线程，请求了多少资源
	printf ("please input the process No. that request new resources\n");
	scanf("%d", &id);

	printf ("please input the request vector\n");
	for (j=0;j<m;j++)
		scanf("%d", &request[j]);

	
	printf ("#########################################\n");
	printf ("Allocation Matrix\n");
	for (i=0;i<n;i++)
	{
		printf ("P%d\t\t",i);
		for (j=0;j<m;j++)
			printf ("%d\t",allocation[i][j]); 
		printf ("\n");
	}

	printf ("Max Matrix\n");
	for (i=0;i<n;i++)
	{
		printf ("P%d\t\t",i);
		for (j=0;j<m;j++)
			printf ("%d\t",max[i][j]); 
		printf ("\n");
	}

	printf ("Available Vector\n");
	for (j=0;j<m;j++)
		printf ("%d\t",available[j]);
	printf ("\n");

	printf ("with additional resouces requests\nP%d\t\t",id);
	for (j=0;j<m;j++)
		printf ("%d\t", request[j]);
	printf ("\n");

	for (j=0;j<m;j++)
	{
		// 检查请求是否超过need
		if (request[j] > need[id][j]) {
			printf("Request exceeds need!\n");
			return 0;
		}
		// 检查请求是否超过available
		if (request[j] > available[j]) {
			printf("Request exceeds available!\n");
			return 0;
		}
		// 修改available矩阵
		available[j] -= request[j];
		// 修改allocation矩阵
		allocation[id][j] += request[j];
		// 修改need矩阵
		need[id][j] -= request[j];
		
		if (available[j]<0)
		{
			printf ("unsafe!\n");
			return 0;
		}
	}
	
  //n表示线程数目，一开始，每个线程都标记成未完成,finish[i] = 0;
	for (i=0;i<n;i++)
		finish[i] = 0;
	//m表示资源种类数目，例如有ABC三类资源，那么m=2
	//开始时，把available赋值给work。
	for (j=0;j<m;j++)
		work[j] = available[j];
   
  //pay attention to the thread mutual exclusion
	printf ("start...\n");
	pthread_mutex_init(&mutex, NULL);  // 初始化互斥锁
	for (i=0;i<n;i++)
	{
		proc_ids[i] = i;
		pthread_attr_init(&attr[i]);
		pthread_create(&tid[i], &attr[i], start_banker, &proc_ids[i]);
	}
	sleep(1);
	for (i=0;i<n;i++)
	{
		if (finish[i]==0)//最后，只要发现有一个线程的finish=0，就说明没找到机会给该线程分配资源，系统就是不安全的，有可能死锁
		{
			printf ("unsafe!!\n");
			break;
		}
	}
	if (i==n)
		printf ("safe!!\n");
	stop_flag = 1;
	printf ("Finish...\n");
	sleep(1);
	for (i=0;i<n;i++)
		pthread_join(tid[i], NULL);  // 等待线程结束
	pthread_mutex_destroy(&mutex);  // 销毁互斥锁
		
	system("pause");
	return 0;
}

void *start_banker(void *param)
{
	int j;
	int id = *(int *)param; //process id
	
	while (!stop_flag && !finish[id]) 
	{
		for (j=0;j<m;j++)
		{
			if (need[id][j] > work[j]) // 如果需要的资源大于可用的资源，则不能执行
				break;
		}
		//如果每个种类的资源都比较了一遍，发现都不break，那就说明对该线程的每个种类的资源need<work，那么这个线程可以执行。
		if (j==m) // need <= work
		{
			pthread_mutex_lock(&mutex);  // 加锁保护共享资源
			// 更新work矩阵
			for (j=0; j<m; j++) {
				work[j] += allocation[id][j];
			}
			finish[id] = 1;  // 标记该进程已完成
			pthread_mutex_unlock(&mutex);  // 解锁
			
			printf ("P%d finished\n",id);
		}
	}
	pthread_exit(0);
}
