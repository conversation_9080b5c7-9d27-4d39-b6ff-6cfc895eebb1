#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>

/**********************************************************************************
银行家算法是用于避免死锁的，本次实验，我们要模拟出这样的一种有可能产生死锁的情形：
系统中目前有很多线程，每个线程都有自己的最大资源需求、也已经分配了一些资源、还需要一些资源。
在这种情形下，系统容易发生死锁(原因思考下)。
然后，我们使用银行家算法来找到这些线程的一种可能的执行顺序，保证不会发生死锁。
************************************************************************************/


/*********************************************************************************
修改为文件输入输出模式，输入文件格式：
第一行：进程数量 资源类型数量
接下来n行：allocation矩阵
接下来n行：max矩阵
接下来一行：available向量
最后两行：请求进程ID 和 请求向量
**********************************************************************************/


void *start_banker(void *param);

#define MAX_PRO 256   /* max process count */
#define MAX_RES 256	/* max resource type */

int available[MAX_RES];
int max[MAX_PRO][MAX_RES];
int allocation[MAX_PRO][MAX_RES];
int need[MAX_PRO][MAX_RES];
int request[MAX_RES];
int work[MAX_RES];
int finish[MAX_PRO];
int stop_flag = 0;
int m,n;
pthread_mutex_t mutex;
int proc_ids[MAX_PRO];

// 文件指针
FILE *input_file;
FILE *output_file;

int main(int argc, char *argv[])
{
	pthread_t tid[MAX_PRO];
	pthread_attr_t attr[MAX_PRO];

	int id;
	int i,j;

	// 打开输入文件
	input_file = fopen("input.txt", "r");
	if (input_file == NULL) {
		printf("无法打开输入文件 input.txt\n");
		return -1;
	}

	// 打开输出文件
	output_file = fopen("output.txt", "w");
	if (output_file == NULL) {
		printf("无法创建输出文件 output.txt\n");
		fclose(input_file);
		return -1;
	}

	// 从文件读取进程数量和资源类型数量
	if (fscanf(input_file, "%d %d", &n, &m) != 2) {
		printf("读取进程数量和资源类型数量失败\n");
		fclose(input_file);
		fclose(output_file);
		return -1;
	}

	fprintf(output_file, "进程数量: %d, 资源类型数量: %d\n", n, m);
	printf("进程数量: %d, 资源类型数量: %d\n", n, m);

	if (m>256 || n>256 || m<0 || n<0)
	{
		printf("输入错误：进程数量或资源类型数量超出范围\n");
		fprintf(output_file, "输入错误：进程数量或资源类型数量超出范围\n");
		fclose(input_file);
		fclose(output_file);
		return -1;
	}

	// 从文件读取已分配资源矩阵
	fprintf(output_file, "读取 allocation 矩阵:\n");
	printf("读取 allocation 矩阵:\n");
	for (i=0;i<n;i++) {
		for (j=0;j<m;j++) {
			if (fscanf(input_file, "%d", &allocation[i][j]) != 1) {
				printf("读取 allocation 矩阵失败\n");
				fclose(input_file);
				fclose(output_file);
				return -1;
			}
		}
	}

	// 从文件读取最大资源需求矩阵
	fprintf(output_file, "读取 max 矩阵:\n");
	printf("读取 max 矩阵:\n");
	for (i=0;i<n;i++) {
		for (j=0;j<m;j++) {
			if (fscanf(input_file, "%d", &max[i][j]) != 1) {
				printf("读取 max 矩阵失败\n");
				fclose(input_file);
				fclose(output_file);
				return -1;
			}
		}
	}

	// 从文件读取可用资源向量
	fprintf(output_file, "读取 available 向量:\n");
	printf("读取 available 向量:\n");
	for (j=0;j<m;j++) {
		if (fscanf(input_file, "%d", &available[j]) != 1) {
			printf("读取 available 向量失败\n");
			fclose(input_file);
			fclose(output_file);
			return -1;
		}
	}

	for (i=0;i<n;i++)
		for (j=0;j<m;j++)
			need[i][j] = max[i][j] - allocation[i][j];  // 计算need矩阵

	// 从文件读取请求进程ID
	if (fscanf(input_file, "%d", &id) != 1) {
		printf("读取请求进程ID失败\n");
		fclose(input_file);
		fclose(output_file);
		return -1;
	}

	// 从文件读取请求向量
	for (j=0;j<m;j++) {
		if (fscanf(input_file, "%d", &request[j]) != 1) {
			printf("读取请求向量失败\n");
			fclose(input_file);
			fclose(output_file);
			return -1;
		}
	}

	// 关闭输入文件
	fclose(input_file);


	printf ("#########################################\n");
	fprintf(output_file, "#########################################\n");

	printf ("Allocation Matrix\n");
	fprintf(output_file, "Allocation Matrix\n");
	for (i=0;i<n;i++)
	{
		printf ("P%d\t\t",i);
		fprintf(output_file, "P%d\t\t",i);
		for (j=0;j<m;j++) {
			printf ("%d\t",allocation[i][j]);
			fprintf(output_file, "%d\t",allocation[i][j]);
		}
		printf ("\n");
		fprintf(output_file, "\n");
	}

	printf ("Max Matrix\n");
	fprintf(output_file, "Max Matrix\n");
	for (i=0;i<n;i++)
	{
		printf ("P%d\t\t",i);
		fprintf(output_file, "P%d\t\t",i);
		for (j=0;j<m;j++) {
			printf ("%d\t",max[i][j]);
			fprintf(output_file, "%d\t",max[i][j]);
		}
		printf ("\n");
		fprintf(output_file, "\n");
	}

	printf ("Available Vector\n");
	fprintf(output_file, "Available Vector\n");
	for (j=0;j<m;j++) {
		printf ("%d\t",available[j]);
		fprintf(output_file, "%d\t",available[j]);
	}
	printf ("\n");
	fprintf(output_file, "\n");

	printf ("with additional resouces requests\nP%d\t\t",id);
	fprintf(output_file, "with additional resouces requests\nP%d\t\t",id);
	for (j=0;j<m;j++) {
		printf ("%d\t", request[j]);
		fprintf(output_file, "%d\t", request[j]);
	}
	printf ("\n");
	fprintf(output_file, "\n");

	for (j=0;j<m;j++)
	{
		// 检查请求是否超过need
		if (request[j] > need[id][j]) {
			printf("Request exceeds need!\n");
			fprintf(output_file, "Request exceeds need!\n");
			fclose(output_file);
			return 0;
		}
		// 检查请求是否超过available
		if (request[j] > available[j]) {
			printf("Request exceeds available!\n");
			fprintf(output_file, "Request exceeds available!\n");
			fclose(output_file);
			return 0;
		}
		// 修改available矩阵
		available[j] -= request[j];
		// 修改allocation矩阵
		allocation[id][j] += request[j];
		// 修改need矩阵
		need[id][j] -= request[j];

		if (available[j]<0)
		{
			printf ("unsafe!\n");
			fprintf(output_file, "unsafe!\n");
			fclose(output_file);
			return 0;
		}
	}
	
  //n表示线程数目，一开始，每个线程都标记成未完成,finish[i] = 0;
	for (i=0;i<n;i++)
		finish[i] = 0;
	//m表示资源种类数目，例如有ABC三类资源，那么m=2
	//开始时，把available赋值给work。
	for (j=0;j<m;j++)
		work[j] = available[j];
   
  //pay attention to the thread mutual exclusion
	printf ("start...\n");
	fprintf(output_file, "start...\n");
	pthread_mutex_init(&mutex, NULL);  // 初始化互斥锁
	for (i=0;i<n;i++)
	{
		proc_ids[i] = i;
		pthread_attr_init(&attr[i]);
		pthread_create(&tid[i], &attr[i], start_banker, &proc_ids[i]);
	}
	sleep(1);
	for (i=0;i<n;i++)
	{
		if (finish[i]==0)//最后，只要发现有一个线程的finish=0，就说明没找到机会给该线程分配资源，系统就是不安全的，有可能死锁
		{
			printf ("unsafe!!\n");
			fprintf(output_file, "unsafe!!\n");
			break;
		}
	}
	if (i==n) {
		printf ("safe!!\n");
		fprintf(output_file, "safe!!\n");
	}
	stop_flag = 1;
	printf ("Finish...\n");
	fprintf(output_file, "Finish...\n");
	sleep(1);
	for (i=0;i<n;i++)
		pthread_join(tid[i], NULL);  // 等待线程结束
	pthread_mutex_destroy(&mutex);  // 销毁互斥锁

	// 关闭输出文件
	fclose(output_file);

	system("pause");
	return 0;
}

void *start_banker(void *param)
{
	int j;
	int id = *(int *)param; //process id

	while (!stop_flag && !finish[id])
	{
		for (j=0;j<m;j++)
		{
			if (need[id][j] > work[j]) // 如果需要的资源大于可用的资源，则不能执行
				break;
		}
		//如果每个种类的资源都比较了一遍，发现都不break，那就说明对该线程的每个种类的资源need<work，那么这个线程可以执行。
		if (j==m) // need <= work
		{
			pthread_mutex_lock(&mutex);  // 加锁保护共享资源

			// 检查输出文件是否仍然打开
			if (output_file != NULL) {
				// 更新work矩阵
				for (j=0; j<m; j++) {
					work[j] += allocation[id][j];
				}
				finish[id] = 1;  // 标记该进程已完成

				printf ("P%d finished\n",id);
				fprintf(output_file, "P%d finished\n",id);
				fflush(output_file);  // 立即刷新输出缓冲区
			}

			pthread_mutex_unlock(&mutex);  // 解锁
		}
	}
	pthread_exit(0);
}
